#!/usr/bin/env python3
"""
Google Drive 文件操作範例
包含上傳、下載、搜尋、建立資料夾等功能
"""

import os
import io
from googleapiclient.http import MediaIoBaseDownload, MediaFileUpload
from googleapiclient.errors import HttpError
from gdrive_connect import authenticate_gdrive


def upload_file(service, file_path, folder_id=None):
    """
    上傳文件到 Google Drive
    
    Args:
        service: Google Drive API 服務物件
        file_path: 本地文件路徑
        folder_id: 目標資料夾 ID (可選)
    
    Returns:
        str: 上傳文件的 ID
    """
    try:
        file_name = os.path.basename(file_path)
        
        # 文件元數據
        file_metadata = {'name': file_name}
        if folder_id:
            file_metadata['parents'] = [folder_id]
        
        # 媒體上傳
        media = MediaFileUpload(file_path, resumable=True)
        
        # 執行上傳
        file = service.files().create(
            body=file_metadata,
            media_body=media,
            fields='id'
        ).execute()
        
        file_id = file.get('id')
        print(f"✅ 文件 '{file_name}' 上傳成功! ID: {file_id}")
        return file_id
    
    except HttpError as error:
        print(f"❌ 上傳文件時發生錯誤: {error}")
        return None
    except FileNotFoundError:
        print(f"❌ 找不到文件: {file_path}")
        return None


def download_file(service, file_id, save_path):
    """
    從 Google Drive 下載文件
    
    Args:
        service: Google Drive API 服務物件
        file_id: 文件 ID
        save_path: 儲存路徑
    """
    try:
        # 獲取文件元數據
        file_metadata = service.files().get(fileId=file_id).execute()
        file_name = file_metadata.get('name')
        
        # 請求文件內容
        request = service.files().get_media(fileId=file_id)
        file_io = io.BytesIO()
        downloader = MediaIoBaseDownload(file_io, request)
        
        done = False
        while done is False:
            status, done = downloader.next_chunk()
            print(f"下載進度: {int(status.progress() * 100)}%")
        
        # 儲存文件
        with open(save_path, 'wb') as f:
            f.write(file_io.getvalue())
        
        print(f"✅ 文件 '{file_name}' 下載成功! 儲存至: {save_path}")
    
    except HttpError as error:
        print(f"❌ 下載文件時發生錯誤: {error}")


def create_folder(service, folder_name, parent_folder_id=None):
    """
    在 Google Drive 建立資料夾
    
    Args:
        service: Google Drive API 服務物件
        folder_name: 資料夾名稱
        parent_folder_id: 父資料夾 ID (可選)
    
    Returns:
        str: 建立的資料夾 ID
    """
    try:
        file_metadata = {
            'name': folder_name,
            'mimeType': 'application/vnd.google-apps.folder'
        }
        
        if parent_folder_id:
            file_metadata['parents'] = [parent_folder_id]
        
        folder = service.files().create(
            body=file_metadata,
            fields='id'
        ).execute()
        
        folder_id = folder.get('id')
        print(f"✅ 資料夾 '{folder_name}' 建立成功! ID: {folder_id}")
        return folder_id
    
    except HttpError as error:
        print(f"❌ 建立資料夾時發生錯誤: {error}")
        return None


def search_files(service, query, max_results=10):
    """
    搜尋 Google Drive 中的文件
    
    Args:
        service: Google Drive API 服務物件
        query: 搜尋查詢
        max_results: 最大結果數量
    
    Returns:
        list: 搜尋結果
    """
    try:
        results = service.files().list(
            q=query,
            pageSize=max_results,
            fields="nextPageToken, files(id, name, mimeType, parents)"
        ).execute()
        
        items = results.get('files', [])
        
        if not items:
            print(f'🔍 沒有找到符合 "{query}" 的文件。')
            return []
        
        print(f'\n🔍 搜尋結果 "{query}":')
        print('-' * 60)
        
        for item in items:
            print(f"📄 {item['name']} (ID: {item['id']})")
            print(f"   類型: {item.get('mimeType', 'unknown')}")
        
        return items
    
    except HttpError as error:
        print(f"❌ 搜尋文件時發生錯誤: {error}")
        return []


def delete_file(service, file_id):
    """
    刪除 Google Drive 中的文件
    
    Args:
        service: Google Drive API 服務物件
        file_id: 文件 ID
    """
    try:
        # 獲取文件名稱
        file_metadata = service.files().get(fileId=file_id).execute()
        file_name = file_metadata.get('name')
        
        # 刪除文件
        service.files().delete(fileId=file_id).execute()
        print(f"✅ 文件 '{file_name}' 刪除成功!")
    
    except HttpError as error:
        print(f"❌ 刪除文件時發生錯誤: {error}")


def demo_operations():
    """
    演示各種 Google Drive 操作
    """
    print("🚀 Google Drive 操作演示")
    
    # 連接到 Google Drive
    service = authenticate_gdrive()
    if not service:
        return
    
    print("\n" + "="*50)
    print("📋 可用操作:")
    print("1. 列出文件")
    print("2. 搜尋文件")
    print("3. 建立資料夾")
    print("4. 上傳文件")
    print("5. 下載文件")
    print("6. 刪除文件")
    print("0. 退出")
    print("="*50)
    
    while True:
        try:
            choice = input("\n請選擇操作 (0-6): ").strip()
            
            if choice == '0':
                print("👋 再見!")
                break
            
            elif choice == '1':
                from gdrive_connect import list_files
                list_files(service)
            
            elif choice == '2':
                query = input("請輸入搜尋關鍵字: ").strip()
                if query:
                    search_files(service, f"name contains '{query}'")
            
            elif choice == '3':
                folder_name = input("請輸入資料夾名稱: ").strip()
                if folder_name:
                    create_folder(service, folder_name)
            
            elif choice == '4':
                file_path = input("請輸入要上傳的文件路徑: ").strip()
                if file_path and os.path.exists(file_path):
                    upload_file(service, file_path)
                else:
                    print("❌ 文件不存在!")
            
            elif choice == '5':
                file_id = input("請輸入文件 ID: ").strip()
                save_path = input("請輸入儲存路徑: ").strip()
                if file_id and save_path:
                    download_file(service, file_id, save_path)
            
            elif choice == '6':
                file_id = input("請輸入要刪除的文件 ID: ").strip()
                if file_id:
                    confirm = input(f"確定要刪除文件 {file_id}? (y/N): ").strip().lower()
                    if confirm == 'y':
                        delete_file(service, file_id)
            
            else:
                print("❌ 無效的選擇，請重新輸入。")
        
        except KeyboardInterrupt:
            print("\n👋 再見!")
            break
        except Exception as e:
            print(f"❌ 發生錯誤: {e}")


if __name__ == '__main__':
    demo_operations()
