#!/usr/bin/env python3
"""
Google Drive API 連接腳本
使用 OAuth2 憑證連接到 Google Drive
"""

import os
import pickle
from google.auth.transport.requests import Request
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError

# Google Drive API 的權限範圍
# 如果修改這些範圍，需要刪除 token.pickle 文件
SCOPES = ['https://www.googleapis.com/auth/drive']

# OAuth2 憑證文件名
CREDENTIALS_FILE = 'client_secret_635820492105-gpcpt34715m2la63gnqosq3ljoacs4oj.apps.googleusercontent.com.json'

# Token 儲存文件名
TOKEN_FILE = 'token.pickle'


def authenticate_gdrive():
    """
    驗證並建立 Google Drive API 服務
    
    Returns:
        service: Google Drive API 服務物件
    """
    creds = None
    
    # token.pickle 儲存用戶的訪問和刷新令牌
    if os.path.exists(TOKEN_FILE):
        with open(TOKEN_FILE, 'rb') as token:
            creds = pickle.load(token)
    
    # 如果沒有有效的憑證，讓用戶登錄
    if not creds or not creds.valid:
        if creds and creds.expired and creds.refresh_token:
            creds.refresh(Request())
        else:
            flow = InstalledAppFlow.from_client_secrets_file(
                CREDENTIALS_FILE, SCOPES)
            creds = flow.run_local_server(port=0)
        
        # 儲存憑證以供下次運行使用
        with open(TOKEN_FILE, 'wb') as token:
            pickle.dump(creds, token)
    
    try:
        # 建立 Drive API 服務
        service = build('drive', 'v3', credentials=creds)
        print("✅ 成功連接到 Google Drive!")
        return service
    
    except HttpError as error:
        print(f"❌ 發生錯誤: {error}")
        return None


def list_files(service, max_results=10):
    """
    列出 Google Drive 中的文件
    
    Args:
        service: Google Drive API 服務物件
        max_results: 最大結果數量
    """
    try:
        # 調用 Drive v3 API
        results = service.files().list(
            pageSize=max_results,
            fields="nextPageToken, files(id, name, mimeType, size, modifiedTime)"
        ).execute()
        
        items = results.get('files', [])
        
        if not items:
            print('📁 沒有找到文件。')
            return
        
        print(f'\n📋 Google Drive 文件列表 (前 {max_results} 個):')
        print('-' * 80)
        print(f"{'文件名':<30} {'類型':<25} {'大小':<15} {'修改時間':<20}")
        print('-' * 80)
        
        for item in items:
            name = item['name'][:28] + '...' if len(item['name']) > 30 else item['name']
            mime_type = item.get('mimeType', 'unknown')
            size = item.get('size', 'N/A')
            if size != 'N/A':
                size = f"{int(size):,} bytes" if size.isdigit() else size
            modified_time = item.get('modifiedTime', 'N/A')[:19] if item.get('modifiedTime') else 'N/A'
            
            print(f"{name:<30} {mime_type:<25} {size:<15} {modified_time:<20}")
    
    except HttpError as error:
        print(f"❌ 列出文件時發生錯誤: {error}")


def get_drive_info(service):
    """
    獲取 Google Drive 儲存空間資訊
    
    Args:
        service: Google Drive API 服務物件
    """
    try:
        about = service.about().get(fields="storageQuota, user").execute()
        
        storage_quota = about.get('storageQuota', {})
        user = about.get('user', {})
        
        print(f"\n👤 用戶資訊:")
        print(f"   名稱: {user.get('displayName', 'N/A')}")
        print(f"   郵箱: {user.get('emailAddress', 'N/A')}")
        
        print(f"\n💾 儲存空間資訊:")
        limit = int(storage_quota.get('limit', 0))
        usage = int(storage_quota.get('usage', 0))
        
        if limit > 0:
            limit_gb = limit / (1024**3)
            usage_gb = usage / (1024**3)
            usage_percent = (usage / limit) * 100
            
            print(f"   總容量: {limit_gb:.2f} GB")
            print(f"   已使用: {usage_gb:.2f} GB ({usage_percent:.1f}%)")
            print(f"   剩餘: {(limit_gb - usage_gb):.2f} GB")
        else:
            print(f"   已使用: {usage / (1024**3):.2f} GB")
            print(f"   總容量: 無限制")
    
    except HttpError as error:
        print(f"❌ 獲取 Drive 資訊時發生錯誤: {error}")


def main():
    """
    主函數
    """
    print("🚀 正在連接 Google Drive...")
    
    # 檢查憑證文件是否存在
    if not os.path.exists(CREDENTIALS_FILE):
        print(f"❌ 找不到憑證文件: {CREDENTIALS_FILE}")
        print("請確保 OAuth2 JSON 文件在當前目錄中。")
        return
    
    # 驗證並建立服務
    service = authenticate_gdrive()
    
    if service:
        # 獲取 Drive 資訊
        get_drive_info(service)
        
        # 列出文件
        list_files(service)
        
        print("\n✨ Google Drive 連接測試完成!")
        print(f"💡 Token 已儲存在 {TOKEN_FILE}，下次運行將自動使用。")
    else:
        print("❌ 無法建立 Google Drive 連接。")


if __name__ == '__main__':
    main()
